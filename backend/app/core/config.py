from pydantic_settings import BaseSettings
from typing import List
import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # 基本设置
    PROJECT_NAME: str = "P-Box Update Server"
    PROJECT_DESCRIPTION: str = "P-Box 更新服务器用于 Tauri 应用的版本管理和更新分发"
    VERSION: str = "0.1.0"
    
    # API设置
    API_PREFIX: str = "/p-box/api"
    ADMIN_PREFIX: str = "/p-box/admin"
    
    # 服务器设置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 域名设置
    DOMAIN: str = "http://localhost:8000"  # 部署时需要修改为实际域名
    
    # 数据库设置
    DATABASE_URL: str = "sqlite:///./p-box.db"
    
    # CORS设置
    CORS_ORIGINS: List[str] = [
        "http://localhost",
        "http://localhost:8000",
        "http://localhost:5173",
        "http://127.0.0.1",
        "http://127.0.0.1:8000",
        "http://127.0.0.1:5173",
        "http://localhost:63342"
    ]
    
    # 文件存储设置
    UPLOAD_DIR: str = "uploads"
    MAX_UPLOAD_SIZE: int = 1024 * 1024 * 100  # 100MB
    
    # 安全设置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()

# 确保上传目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)