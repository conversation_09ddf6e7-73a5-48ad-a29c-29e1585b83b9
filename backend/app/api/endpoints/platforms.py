from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import List

from app.db.session import get_db
from app.models.platform import Platform
from app.schemas.platform import PlatformSchema, PlatformCreate, PlatformUpdate

router = APIRouter()

@router.get("/")
def get_platforms(skip: int = 0, limit: int = 100, search: str = "", db: Session = Depends(get_db)):
    """获取所有平台列表"""
    query = db.query(Platform)
    
    # 添加搜索功能
    if search:
        query = query.filter(
            Platform.name.ilike(f"%{search}%") |
            Platform.identifier.ilike(f"%{search}%") |
            Platform.description.ilike(f"%{search}%")
        )
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    platforms = query.offset(skip).limit(limit).all()
    
    # 计算分页信息
    total_pages = (total + limit - 1) // limit
    current_page = (skip // limit) + 1
    
    return {
        "items": platforms,
        "total": total,
        "totalPages": total_pages,
        "currentPage": current_page,
        "pageSize": limit
    }

@router.get("/{platform_id}", response_model=PlatformSchema)
def get_platform(platform_id: int, db: Session = Depends(get_db)):
    """获取特定平台详情"""
    platform = db.query(Platform).filter(Platform.id == platform_id).first()
    if platform is None:
        raise HTTPException(status_code=404, detail="Platform not found")
    return platform

@router.post("/", response_model=PlatformSchema)
def create_platform(platform: PlatformCreate, db: Session = Depends(get_db)):
    """创建新平台"""
    try:
        db_platform = Platform(**platform.dict())
        db.add(db_platform)
        db.commit()
        db.refresh(db_platform)
        return db_platform
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Platform identifier already exists")

@router.put("/{platform_id}", response_model=PlatformSchema)
def update_platform(platform_id: int, platform: PlatformUpdate, db: Session = Depends(get_db)):
    """更新平台信息"""
    db_platform = db.query(Platform).filter(Platform.id == platform_id).first()
    if db_platform is None:
        raise HTTPException(status_code=404, detail="Platform not found")
    
    try:
        update_data = platform.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_platform, field, value)
        
        db.commit()
        db.refresh(db_platform)
        return db_platform
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Platform identifier already exists")

@router.delete("/{platform_id}")
def delete_platform(platform_id: int, db: Session = Depends(get_db)):
    """删除平台"""
    db_platform = db.query(Platform).filter(Platform.id == platform_id).first()
    if db_platform is None:
        raise HTTPException(status_code=404, detail="Platform not found")
    
    # 检查是否有关联的发布版本
    if db_platform.releases:
        raise HTTPException(status_code=400, detail="Cannot delete platform with associated releases")
    
    db.delete(db_platform)
    db.commit()
    return {"message": "Platform deleted successfully"}