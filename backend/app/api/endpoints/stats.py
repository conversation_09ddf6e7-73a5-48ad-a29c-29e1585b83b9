from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, distinct
from datetime import datetime, timedelta
import calendar

from app.db.session import get_db
from app.core.auth import get_current_admin_user
from app.models.user import User
from app.models.release import Release
from app.models.platform import Platform
from app.models.architecture import Architecture
from app.models.download_record import DownloadRecord
from app.models.update_check import UpdateCheck

router = APIRouter()


@router.get("/")
async def get_basic_stats(
        db: Session = Depends(get_db)
):
    """获取基础统计数据（无需认证）"""
    # 总下载次数
    total_downloads = db.query(func.count(DownloadRecord.id)).scalar() or 0

    # 本月下载次数
    today = datetime.utcnow().date()
    month_start = today.replace(day=1)
    monthly_downloads = db.query(func.count(DownloadRecord.id)).filter(
        func.date(DownloadRecord.created_at) >= month_start
    ).scalar() or 0

    # 版本数量
    version_count = db.query(func.count(Release.id)).scalar() or 0

    # 平台数量
    platform_count = db.query(func.count(Platform.id)).filter(
        Platform.is_active == True
    ).scalar() or 0

    # 架构数量
    architecture_count = db.query(func.count(Architecture.id)).filter(
        Architecture.is_active == True
    ).scalar() or 0

    # 总更新检查次数
    total_update_checks = db.query(func.count(UpdateCheck.id)).scalar() or 0

    return {
        "total_downloads": total_downloads,
        "monthly_downloads": monthly_downloads,
        "version_count": version_count,
        "platform_count": platform_count,
        "architecture_count": architecture_count,
        "total_update_checks": total_update_checks
    }


@router.get("/admin/download-trends")
async def get_download_trends(
        start_date: str = None,
        end_date: str = None,
        period: str = "week",  # week, month, year
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取下载趋势统计数据"""
    today = datetime.utcnow().date()

    # 处理自定义日期范围
    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )
        date_format = "%Y-%m-%d"
        date_trunc = func.date(DownloadRecord.created_at)
    elif period == "week":
        # 获取过去7天的数据
        start_date = today - timedelta(days=6)
        end_date = today
        date_format = "%Y-%m-%d"
        date_trunc = func.date(DownloadRecord.created_at)
    elif period == "month":
        # 获取过去30天的数据
        start_date = today - timedelta(days=29)
        end_date = today
        date_format = "%Y-%m-%d"
        date_trunc = func.date(DownloadRecord.created_at)
    elif period == "year":
        # 获取过去12个月的数据
        start_date = (today.replace(day=1) - timedelta(days=1)).replace(day=1)
        start_date = start_date.replace(month=((start_date.month - 11) % 12) or 12,
                                        year=start_date.year - ((start_date.month - 11) <= 0))
        end_date = today
        date_format = "%Y-%m"
        date_trunc = func.strftime("%Y-%m", DownloadRecord.created_at)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid period. Must be 'week', 'month', or 'year'"
        )

    # 查询下载记录
    download_counts = db.query(
        date_trunc.label("date"),
        func.count().label("count")
    ).filter(
        DownloadRecord.created_at >= start_date,
        DownloadRecord.created_at <= end_date
    ).group_by(
        "date"
    ).order_by(
        "date"
    ).all()

    # 转换为字典格式
    result_dict = {item.date: item.count for item in download_counts}

    # 生成日期范围并填充缺失的日期
    result = []

    if period == "year":
        # 按月生成日期范围
        current_date = start_date
        for _ in range(12):
            date_str = current_date.strftime(date_format)
            count = result_dict.get(date_str, 0)
            result.append({"date": date_str, "count": count})
            # 移动到下个月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
    else:
        # 按天生成日期范围
        current_date = start_date
        while current_date <= end_date:
            date_str = current_date.strftime(date_format)
            count = result_dict.get(date_str, 0)
            result.append({"date": date_str, "count": count})
            current_date += timedelta(days=1)

    return result


@router.get("/admin/platform-distribution")
async def get_platform_distribution(
        start_date: str = None,
        end_date: str = None,
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取平台分布统计数据"""
    # 构建查询
    query = db.query(
        DownloadRecord.platform.label("platform"),
        func.count().label("count")
    )

    # 添加日期过滤
    if start_date and end_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
            query = query.filter(
                DownloadRecord.created_at >= start_date_obj,
                DownloadRecord.created_at <= end_date_obj
            )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )

    platform_counts = query.group_by(
        DownloadRecord.platform
    ).all()

    # 获取平台名称映射
    platforms = {p.identifier: p.name for p in db.query(Platform).all()}

    # 转换为所需格式
    result = []
    for item in platform_counts:
        platform_name = platforms.get(item.platform, item.platform)
        result.append({"name": platform_name, "value": item.count})

    return result


@router.get("/admin/architecture-distribution")
async def get_architecture_distribution(
        start_date: str = None,
        end_date: str = None,
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取架构分布统计数据"""
    # 构建查询
    query = db.query(
        DownloadRecord.architecture.label("architecture"),
        func.count().label("count")
    )

    # 添加日期过滤
    if start_date and end_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
            query = query.filter(
                DownloadRecord.created_at >= start_date_obj,
                DownloadRecord.created_at <= end_date_obj
            )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )

    arch_counts = query.group_by(
        DownloadRecord.architecture
    ).all()

    # 获取架构名称映射
    architectures = {a.identifier: a.name for a in db.query(Architecture).all()}

    # 转换为所需格式
    result = []
    for item in arch_counts:
        arch_name = architectures.get(item.architecture, item.architecture)
        result.append({"name": arch_name, "value": item.count})

    return result


@router.get("/admin/version-distribution")
async def get_version_distribution(
        start_date: str = None,
        end_date: str = None,
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取版本分布统计数据"""
    # 构建查询
    query = db.query(
        Release.version.label("version"),
        func.count().label("count")
    ).join(
        DownloadRecord, DownloadRecord.release_id == Release.id
    )

    # 添加日期过滤
    if start_date and end_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
            query = query.filter(
                DownloadRecord.created_at >= start_date_obj,
                DownloadRecord.created_at <= end_date_obj
            )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )

    version_counts = query.group_by(
        Release.version
    ).order_by(
        desc(Release.version)
    ).limit(10).all()

    # 转换为所需格式
    result = [{"name": item.version, "value": item.count} for item in version_counts]

    return result


@router.get("/admin/popular-downloads")
async def get_popular_downloads(
        start_date: str = None,
        end_date: str = None,
        limit: int = 5,
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取热门下载统计数据"""
    # 构建查询
    query = db.query(
        Release.id,
        Release.version,
        Release.title,
        func.count().label("download_count")
    ).join(
        DownloadRecord, DownloadRecord.release_id == Release.id
    )

    # 添加日期过滤
    if start_date and end_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
            query = query.filter(
                DownloadRecord.created_at >= start_date_obj,
                DownloadRecord.created_at <= end_date_obj
            )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )
    else:
        # 默认查询最近一周内的热门下载
        one_week_ago = datetime.utcnow() - timedelta(days=7)
        query = query.filter(
            DownloadRecord.created_at >= one_week_ago
        )

    hot_downloads = query.group_by(
        Release.id
    ).order_by(
        desc("download_count")
    ).limit(limit).all()

    # 转换为所需格式
    result = [{
        "id": item.id,
        "version": item.version,
        "title": item.title,
        "download_count": item.download_count
    } for item in hot_downloads]

    return result


@router.get("/admin/recent-versions")
async def get_recent_versions(
        limit: int = 5,
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取最新版本列表"""
    # 查询最新的几个版本
    latest_versions = db.query(
        Release.id,
        Release.version,
        Release.title,
        Release.pub_date,
        func.count(DownloadRecord.id).label("download_count")
    ).outerjoin(
        DownloadRecord, DownloadRecord.release_id == Release.id
    ).group_by(
        Release.id
    ).order_by(
        desc(Release.pub_date)
    ).limit(limit).all()

    # 转换为所需格式
    result = [{
        "id": item.id,
        "version": item.version,
        "title": item.title,
        "pub_date": item.pub_date.isoformat() if item.pub_date else None,
        "download_count": item.download_count
    } for item in latest_versions]

    return result


@router.get("/admin/recent-downloads")
async def get_recent_downloads(
        limit: int = 5,
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取最近下载记录"""
    # 查询最近的下载记录
    recent_downloads = db.query(
        DownloadRecord.id,
        DownloadRecord.client_ip,
        DownloadRecord.platform,
        DownloadRecord.architecture,
        DownloadRecord.created_at,
        Release.version,
        Release.title
    ).join(
        Release, DownloadRecord.release_id == Release.id
    ).order_by(
        desc(DownloadRecord.created_at)
    ).limit(limit).all()

    # 转换为所需格式
    result = [{
        "id": item.id,
        "client_ip": item.client_ip,
        "platform": item.platform,
        "architecture": item.architecture,
        "created_at": item.created_at.isoformat() if item.created_at else None,
        "version": item.version,
        "title": item.title
    } for item in recent_downloads]

    return result


@router.get("/admin/platforms")
async def get_admin_platforms(
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取平台列表（管理员）"""
    platforms = db.query(Platform).all()
    return [{
        "id": p.id,
        "name": p.name,
        "identifier": p.identifier,
        "description": p.description,
        "is_active": p.is_active
    } for p in platforms]


@router.get("/admin/architectures")
async def get_admin_architectures(
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取架构列表（管理员）"""
    architectures = db.query(Architecture).all()
    return [{
        "id": a.id,
        "name": a.name,
        "identifier": a.identifier,
        "description": a.description,
        "is_active": a.is_active
    } for a in architectures]


@router.get("/summary")
async def get_summary_stats(
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取统计摘要数据"""
    # 总下载次数
    total_downloads = db.query(func.count(DownloadRecord.id)).scalar() or 0

    # 今日下载次数
    today = datetime.utcnow().date()
    today_downloads = db.query(func.count(DownloadRecord.id)).filter(
        func.date(DownloadRecord.created_at) == today
    ).scalar() or 0

    # 本周下载次数
    week_start = today - timedelta(days=today.weekday())
    week_downloads = db.query(func.count(DownloadRecord.id)).filter(
        func.date(DownloadRecord.created_at) >= week_start
    ).scalar() or 0

    # 本月下载次数
    month_start = today.replace(day=1)
    month_downloads = db.query(func.count(DownloadRecord.id)).filter(
        func.date(DownloadRecord.created_at) >= month_start
    ).scalar() or 0

    # 总版本数
    total_versions = db.query(func.count(Release.id)).scalar() or 0

    # 总更新检查次数
    total_update_checks = db.query(func.count(UpdateCheck.id)).scalar() or 0

    # 独立IP数
    unique_ips = db.query(func.count(func.distinct(DownloadRecord.client_ip))).scalar() or 0

    # 返回统计摘要
    return {
        "total_downloads": total_downloads,
        "today_downloads": today_downloads,
        "week_downloads": week_downloads,
        "month_downloads": month_downloads,
        "total_versions": total_versions,
        "total_update_checks": total_update_checks,
        "unique_ips": unique_ips
    }


@router.get("/admin/daily-unique-ips")
async def get_daily_unique_ips(
        start_date: str = None,
        end_date: str = None,
        period: str = "week",  # week, month, year
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取每日独立IP统计数据（按平台分组）"""
    today = datetime.utcnow().date()

    # 处理自定义日期范围
    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )
    elif period == "week":
        # 获取过去7天的数据
        start_date = today - timedelta(days=6)
        end_date = today
    elif period == "month":
        # 获取过去30天的数据
        start_date = today - timedelta(days=29)
        end_date = today
    elif period == "year":
        # 获取过去365天的数据
        start_date = today - timedelta(days=364)
        end_date = today
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid period. Must be 'week', 'month', or 'year'"
        )


    # 查询每日独立IP数据（按平台分组）
    ip_stats = db.query(
        func.date(UpdateCheck.created_at).label("date"),
        UpdateCheck.platform.label("platform"),
        func.count(func.distinct(UpdateCheck.client_ip)).label("unique_ips")
    ).filter(
        UpdateCheck.created_at >= start_date
    ).group_by(
        func.date(UpdateCheck.created_at),
        UpdateCheck.platform
    ).order_by(
        func.date(UpdateCheck.created_at)
    ).all()

    # 获取平台名称映射
    platforms = {p.identifier: p.name for p in db.query(Platform).all()}



    # 组织数据结构
    result_dict = {}
    platform_set = set()

    for item in ip_stats:
        # func.date() 返回的是字符串，不需要 strftime 转换
        date_str = item.date
        platform_name = platforms.get(item.platform, item.platform)
        platform_set.add(platform_name)

        if date_str not in result_dict:
            result_dict[date_str] = {}
        result_dict[date_str][platform_name] = item.unique_ips

    # 生成完整的日期范围并填充缺失的数据
    result = []
    current_date = start_date

    while current_date <= end_date:
        date_str = current_date.strftime("%Y-%m-%d")
        date_data = {"date": date_str}

        # 为每个平台添加数据，缺失的设为0
        for platform in platform_set:
            date_data[platform] = result_dict.get(date_str, {}).get(platform, 0)

        # 计算总数
        date_data["total"] = sum(date_data.get(platform, 0) for platform in platform_set)

        result.append(date_data)
        current_date += timedelta(days=1)

    return {
        "data": result,
        "platforms": list(platform_set)
    }


@router.get("/admin/update-checks-chart")
async def get_update_checks_chart(
        start_date: str = None,
        end_date: str = None,
        period: str = "week",  # week, month, year
        current_user: User = Depends(get_current_admin_user),
        db: Session = Depends(get_db)
):
    """获取更新检查记录的柱状图数据（按日期和平台分组）"""
    today = datetime.utcnow().date()

    # 处理自定义日期范围
    if start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date format. Use YYYY-MM-DD"
            )
    elif period == "week":
        # 获取过去7天的数据
        start_date = today - timedelta(days=6)
        end_date = today
    elif period == "month":
        # 获取过去30天的数据
        start_date = today - timedelta(days=29)
        end_date = today
    elif period == "year":
        # 获取过去365天的数据
        start_date = today - timedelta(days=364)
        end_date = today
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid period. Must be 'week', 'month', or 'year'"
        )

    # 查询更新检查记录数据（按日期和平台分组）
    update_check_stats = db.query(
        func.date(UpdateCheck.created_at).label("date"),
        UpdateCheck.platform.label("platform"),
        func.count(UpdateCheck.id).label("count")
    ).filter(
        UpdateCheck.created_at >= start_date,
        UpdateCheck.created_at <= end_date
    ).group_by(
        func.date(UpdateCheck.created_at),
        UpdateCheck.platform
    ).order_by(
        func.date(UpdateCheck.created_at)
    ).all()

    # 获取平台名称映射
    platforms = {p.identifier: p.name for p in db.query(Platform).all()}

    # 组织数据结构
    result_dict = {}
    platform_set = set()

    for item in update_check_stats:
        # func.date() 返回的是字符串，不需要 strftime 转换
        date_str = item.date
        platform_name = platforms.get(item.platform, item.platform)
        platform_set.add(platform_name)

        if date_str not in result_dict:
            result_dict[date_str] = {}
        result_dict[date_str][platform_name] = item.count

    # 生成完整的日期范围并填充缺失的数据
    result = []
    current_date = start_date

    while current_date <= end_date:
        date_str = current_date.strftime("%Y-%m-%d")
        date_data = {"date": date_str}

        # 为每个平台添加数据，缺失的设为0
        for platform in platform_set:
            date_data[platform] = result_dict.get(date_str, {}).get(platform, 0)

        # 计算总数
        date_data["total"] = sum(date_data.get(platform, 0) for platform in platform_set)

        result.append(date_data)
        current_date += timedelta(days=1)

    return {
        "data": result,
        "platforms": list(platform_set)
    }
