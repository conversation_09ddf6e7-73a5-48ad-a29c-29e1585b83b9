from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import os
from loguru import logger

from app.db.session import get_db
from app.models.release import Release
from app.models.download_record import DownloadRecord
from app.core.config import settings

router = APIRouter()


@router.get("/{version}/{platform}/{arch}/")
async def download_update(
        version: str,
        platform: str,
        arch: str,
        request: Request = None,
        db: Session = Depends(get_db)
):
    """下载特定版本的更新文件"""
    # 查询指定版本
    from app.models.platform import Platform
    from app.models.architecture import Architecture

    logger.info(f"version: {version}")
    logger.info(f"platform: {platform}")
    logger.info(f"arch: {arch}")

    release = (db.query(Release)
               .join(Release.platforms)
               .join(Release.architectures)
               .filter(Release.version == version,
                       Release.is_active == True,
                       Platform.identifier == platform,
                       Architecture.identifier == arch).first())



    if not release:
        raise HTTPException(status_code=404, detail="Release not found")

    # 获取客户端IP
    client_ip = request.client.host if request else None

    # 记录下载
    download_record = DownloadRecord(
        release_id=release.id,
        client_ip=client_ip,
        platform=platform,
        architecture=arch
    )
    db.add(download_record)
    db.commit()

    # 构建文件路径
    file_path = release.update_file
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"Update file not found: {file_path}")
        raise HTTPException(status_code=404, detail="Update file not found")

    # 返回文件
    return FileResponse(
        path=file_path,
        filename=os.path.basename(file_path),
        media_type="application/octet-stream"
    )
