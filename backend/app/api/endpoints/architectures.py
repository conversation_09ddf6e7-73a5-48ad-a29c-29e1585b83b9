from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import List

from app.db.session import get_db
from app.models.architecture import Architecture
from app.schemas.architecture import ArchitectureSchema, ArchitectureCreate, ArchitectureUpdate

router = APIRouter()

@router.get("/")
def get_architectures(skip: int = 0, limit: int = 100, search: str = "", db: Session = Depends(get_db)):
    """获取所有架构列表"""
    query = db.query(Architecture)
    
    # 添加搜索功能
    if search:
        query = query.filter(
            Architecture.name.ilike(f"%{search}%") |
            Architecture.identifier.ilike(f"%{search}%") |
            Architecture.description.ilike(f"%{search}%")
        )
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    architectures = query.offset(skip).limit(limit).all()
    
    # 计算分页信息
    total_pages = (total + limit - 1) // limit
    current_page = (skip // limit) + 1
    
    return {
        "items": architectures,
        "total": total,
        "totalPages": total_pages,
        "currentPage": current_page,
        "pageSize": limit
    }

@router.get("/{architecture_id}", response_model=ArchitectureSchema)
def get_architecture(architecture_id: int, db: Session = Depends(get_db)):
    """获取特定架构详情"""
    architecture = db.query(Architecture).filter(Architecture.id == architecture_id).first()
    if architecture is None:
        raise HTTPException(status_code=404, detail="Architecture not found")
    return architecture

@router.post("/", response_model=ArchitectureSchema)
def create_architecture(architecture: ArchitectureCreate, db: Session = Depends(get_db)):
    """创建新架构"""
    try:
        db_architecture = Architecture(**architecture.dict())
        db.add(db_architecture)
        db.commit()
        db.refresh(db_architecture)
        return db_architecture
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Architecture identifier already exists")

@router.put("/{architecture_id}", response_model=ArchitectureSchema)
def update_architecture(architecture_id: int, architecture: ArchitectureUpdate, db: Session = Depends(get_db)):
    """更新架构信息"""
    db_architecture = db.query(Architecture).filter(Architecture.id == architecture_id).first()
    if db_architecture is None:
        raise HTTPException(status_code=404, detail="Architecture not found")
    
    try:
        update_data = architecture.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_architecture, field, value)
        
        db.commit()
        db.refresh(db_architecture)
        return db_architecture
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Architecture identifier already exists")

@router.delete("/{architecture_id}")
def delete_architecture(architecture_id: int, db: Session = Depends(get_db)):
    """删除架构"""
    db_architecture = db.query(Architecture).filter(Architecture.id == architecture_id).first()
    if db_architecture is None:
        raise HTTPException(status_code=404, detail="Architecture not found")
    
    # 检查是否有关联的发布版本
    if db_architecture.releases:
        raise HTTPException(status_code=400, detail="Cannot delete architecture with associated releases")
    
    db.delete(db_architecture)
    db.commit()
    return {"message": "Architecture deleted successfully"}