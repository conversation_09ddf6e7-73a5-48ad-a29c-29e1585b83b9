import React, { useState, useEffect } from 'react';
import {
  Package,
  Plus,
  Search,
  Edit,
  Trash2,
  Download,
  Calendar,
  Tag,
  CheckCircle,
  XCircle,
  AlertCircle,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { apiClient } from '../api';
import type { Release, Platform, Architecture, PaginatedResponse } from '../types';
import ReleaseForm from './ReleaseForm';

const Releases: React.FC = () => {
  const [releases, setReleases] = useState<Release[]>([]);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [architectures, setArchitectures] = useState<Architecture[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingRelease, setEditingRelease] = useState<Release | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPlatform, setSelectedPlatform] = useState<number | null>(null);
  const [selectedArchitecture, setSelectedArchitecture] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const pageSize = 10;

  useEffect(() => {
    loadReleases();
    loadPlatformsAndArchitectures();
  }, [currentPage, searchTerm, selectedPlatform, selectedArchitecture]);

  const loadReleases = async () => {
    setLoading(true);
    try {
      const params = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(selectedPlatform && { platform_id: selectedPlatform }),
        ...(selectedArchitecture && { architecture_id: selectedArchitecture })
      };

      const response: PaginatedResponse<Release> = await apiClient.getReleases(params);
      setReleases(response.items);
      setTotalPages(response.totalPages);
      setTotal(response.total);
    } catch (error) {
      console.error('Failed to load releases:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPlatformsAndArchitectures = async () => {
    try {
      const [platformsData, architecturesData] = await Promise.all([
        apiClient.getPlatforms(),
        apiClient.getArchitectures()
      ]);
      setPlatforms(platformsData);
      setArchitectures(architecturesData);
    } catch (error) {
      console.error('Failed to load platforms and architectures:', error);
    }
  };

  const handleCreateRelease = () => {
    setEditingRelease(null);
    setShowForm(true);
  };

  const handleEditRelease = (release: Release) => {
    setEditingRelease(release);
    setShowForm(true);
  };

  const handleDeleteRelease = async (release: Release) => {
    if (!confirm(`确定要删除版本 ${release.version} 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await apiClient.deleteRelease(release.id);
      loadReleases();
    } catch (error) {
      console.error('Failed to delete release:', error);
      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const handleFormSuccess = () => {
    loadReleases();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    loadReleases();
  };

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedPlatform(null);
    setSelectedArchitecture(null);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">版本管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理应用程序发布版本
          </p>
        </div>
        <button
          onClick={handleCreateRelease}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          发布新版本
        </button>
      </div>

      {/* 搜索和过滤 */}
      <div className="bg-white shadow rounded-lg p-4">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                搜索版本
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="搜索版本号或标题..."
                  className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                平台
              </label>
              <select
                value={selectedPlatform || ''}
                onChange={(e) => setSelectedPlatform(e.target.value ? Number(e.target.value) : null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">所有平台</option>
                {platforms.map(platform => (
                  <option key={platform.id} value={platform.id}>
                    {platform.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                架构
              </label>
              <select
                value={selectedArchitecture || ''}
                onChange={(e) => setSelectedArchitecture(e.target.value ? Number(e.target.value) : null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">所有架构</option>
                {architectures.map(architecture => (
                  <option key={architecture.id} value={architecture.id}>
                    {architecture.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end space-x-2">
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Filter className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={resetFilters}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                重置
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* 版本列表 */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              版本列表
            </h3>
            <span className="text-sm text-gray-500">
              共 {total} 个版本
            </span>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">加载中...</p>
            </div>
          ) : releases.length === 0 ? (
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无版本</h3>
              <p className="mt-1 text-sm text-gray-500">
                点击上方"发布新版本"按钮创建第一个版本
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {releases.map((release) => (
                <div
                  key={release.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="text-lg font-medium text-gray-900 flex items-center">
                          <Tag className="h-4 w-4 mr-1 text-blue-600" />
                          {release.version}
                        </h4>
                        <div className="flex items-center space-x-2">
                          {release.is_active ? (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              启用
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <XCircle className="h-3 w-3 mr-1" />
                              禁用
                            </span>
                          )}
                          {release.is_prerelease && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              预发布
                            </span>
                          )}
                        </div>
                      </div>

                      <h5 className="text-md font-medium text-gray-800 mb-2">
                        {release.title}
                      </h5>

                      {release.notes && (
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {release.notes}
                        </p>
                      )}

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                        <div>
                          <span className="font-medium">平台:</span>
                          <br />
                          {release.platform?.name || '未知'}
                        </div>
                        <div>
                          <span className="font-medium">架构:</span>
                          <br />
                          {release.architecture?.name || '未知'}
                        </div>
                        <div>
                          <span className="font-medium">文件大小:</span>
                          <br />
                          {release.fileSize ? formatFileSize(release.fileSize) : '未知'}
                        </div>
                        <div>
                          <span className="font-medium">下载次数:</span>
                          <br />
                          {release.downloadCount || 0}
                        </div>
                      </div>

                      <div className="mt-3 flex items-center text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-1" />
                        发布时间: {formatDate(release.pub_date)}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {release.download_url && (
                        <a
                          href={release.download_url}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                          title="下载"
                        >
                          <Download className="h-4 w-4" />
                        </a>
                      )}
                      <button
                        onClick={() => handleEditRelease(release)}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        title="编辑"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteRelease(release)}
                        className="inline-flex items-center px-3 py-1 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                        title="删除"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="mt-6 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, total)} 条，共 {total} 条
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="h-4 w-4" />
                  上一页
                </button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white'
                            : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 版本表单弹窗 */}
      <ReleaseForm
        isOpen={showForm}
        onClose={() => setShowForm(false)}
        onSuccess={handleFormSuccess}
        editingRelease={editingRelease}
      />
    </div>
  );
};

export default Releases;
