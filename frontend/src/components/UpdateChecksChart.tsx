import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Calendar, TrendingUp, Loader2 } from 'lucide-react';
import { apiClient } from '../api';
import type { UpdateCheckChartData } from '../types';

interface UpdateChecksChartProps {
  className?: string;
}

const UpdateChecksChart: React.FC<UpdateChecksChartProps> = ({ className = '' }) => {
  const [data, setData] = useState<UpdateCheckChartData[]>([]);
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<string>('week');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  // 平台颜色映射
  const platformColors: { [key: string]: string } = {
    'Windows': '#0078d4',
    'macOS': '#007aff',
    'Linux': '#ff6b35',
    'windows': '#0078d4',
    'macos': '#007aff',
    'linux': '#ff6b35',
  };

  // 获取平台颜色，如果没有预定义则生成随机颜色
  const getPlatformColor = (platform: string, index: number): string => {
    if (platformColors[platform]) {
      return platformColors[platform];
    }
    // 生成基于索引的颜色
    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1', '#d084d0'];
    return colors[index % colors.length];
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.getUpdateChecksChart(
        startDate || undefined,
        endDate || undefined,
        period
      );
      
      setData(response.data);
      setPlatforms(response.platforms);
    } catch (err) {
      console.error('获取更新检查图表数据失败:', err);
      setError('获取图表数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [period, startDate, endDate]);

  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
    // 清空自定义日期范围
    setStartDate('');
    setEndDate('');
  };

  const handleDateRangeChange = () => {
    if (startDate && endDate) {
      fetchData();
    }
  };

  const formatTooltipLabel = (label: string) => {
    return `日期: ${label}`;
  };

  const formatTooltipValue = (value: number, name: string) => {
    return [`${value} 次`, name];
  };

  if (loading) {
    return (
      <div className={`bg-white shadow rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white shadow rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
            <p className="text-gray-500">{error}</p>
            <button
              onClick={fetchData}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white shadow rounded-lg p-6 ${className}`}>
      {/* 标题和控制器 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div className="flex items-center mb-4 sm:mb-0">
          <TrendingUp className="h-5 w-5 text-blue-500 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">
            更新检查统计 (独立IP)
          </h3>
        </div>
        
        {/* 时间范围选择器 */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 预设时间范围 */}
          <div className="flex gap-2">
            {[
              { value: 'week', label: '最近7天' },
              { value: 'month', label: '最近30天' },
              { value: 'year', label: '最近365天' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => handlePeriodChange(option.value)}
                className={`px-3 py-1 text-sm rounded-md ${
                  period === option.value && !startDate && !endDate
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
          
          {/* 自定义日期范围 */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-2 py-1"
            />
            <span className="text-gray-500">至</span>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-2 py-1"
            />
            {startDate && endDate && (
              <button
                onClick={handleDateRangeChange}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                应用
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 图表 */}
      {data.length > 0 ? (
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip 
                labelFormatter={formatTooltipLabel}
                formatter={formatTooltipValue}
              />
              <Legend />
              {platforms.map((platform, index) => (
                <Bar
                  key={platform}
                  dataKey={platform}
                  stackId="platform"
                  fill={getPlatformColor(platform, index)}
                  name={platform}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
            <p className="text-gray-500">选择的时间范围内没有更新检查记录</p>
          </div>
        </div>
      )}
      
      {/* 统计信息 */}
      {data.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-medium text-gray-900">
                {data.reduce((sum, item) => sum + item.total, 0)}
              </div>
              <div className="text-gray-500">总检查次数</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-gray-900">
                {Math.round(data.reduce((sum, item) => sum + item.total, 0) / data.length)}
              </div>
              <div className="text-gray-500">日均检查次数</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-gray-900">
                {Math.max(...data.map(item => item.total))}
              </div>
              <div className="text-gray-500">单日最高</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-gray-900">
                {platforms.length}
              </div>
              <div className="text-gray-500">活跃平台数</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpdateChecksChart;
