import React, { useState, useEffect } from 'react';
import {
  Monitor,
  Cpu,
  Package,
  Activity,
  Download,
  TrendingUp
} from 'lucide-react';
import { apiClient } from '../api';
import type { BasicStats } from '../types';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<BasicStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const data = await apiClient.getBasicStats();
        setStats(data);
        setError(null);
      } catch (err) {
        console.error('获取统计数据失败:', err);
        setError('获取统计数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      name: '支持平台',
      value: stats?.platform_count || 0,
      icon: Monitor,
      color: 'bg-blue-500',
    },
    {
      name: '支持架构',
      value: stats?.architecture_count || 0,
      icon: Cpu,
      color: 'bg-green-500',
    },
    {
      name: '总版本数',
      value: stats?.version_count || 0,
      icon: Package,
      color: 'bg-purple-500',
    },
    {
      name: '本月下载',
      value: stats?.monthly_downloads || 0,
      icon: TrendingUp,
      color: 'bg-orange-500',
    },
    {
      name: '更新检查',
      value: stats?.total_update_checks || 0,
      icon: Activity,
      color: 'bg-red-500',
    },
    {
      name: '总下载次数',
      value: stats?.total_downloads || 0,
      icon: Download,
      color: 'bg-indigo-500',
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
          <p className="mt-1 text-sm text-gray-500">
            P-Box 更新服务器管理系统概览
          </p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
          <p className="mt-1 text-sm text-gray-500">
            P-Box 更新服务器管理系统概览
          </p>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                加载失败
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
        <p className="mt-1 text-sm text-gray-500">
          P-Box 更新服务器管理系统概览
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {statCards.map((card) => {
          const Icon = card.icon;
          return (
            <div
              key={card.name}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className={`absolute ${card.color} rounded-md p-3`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 truncate">
                  {card.name}
                </p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">
                  {card.value}
                </p>
              </dd>
            </div>
          );
        })}
      </div>

      {/* 系统状态 */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          系统状态
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">API 服务</p>
              <p className="text-sm text-gray-500">运行正常</p>
            </div>
          </div>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">数据库</p>
              <p className="text-sm text-gray-500">连接正常</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
