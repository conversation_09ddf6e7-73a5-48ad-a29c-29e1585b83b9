import React, { useState, useEffect } from 'react';
import { X, Upload, Calendar, Tag, FileText, Settings } from 'lucide-react';
import { apiClient } from '../api';
import type { Platform, Architecture, ReleaseFormData, Release } from '../types';

interface ReleaseFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingRelease?: Release | null;
}

const ReleaseForm: React.FC<ReleaseFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  editingRelease
}) => {
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [architectures, setArchitectures] = useState<Architecture[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ReleaseFormData>({
    version: '',
    title: '',
    notes: '',
    platform_ids: [],
    architecture_ids: [],
    update_file: null,
    signature: '',
    is_active: true,
    is_prerelease: false,
    pub_date: new Date().toISOString().slice(0, 16)
  });

  useEffect(() => {
    if (isOpen) {
      loadPlatformsAndArchitectures();
      if (editingRelease) {
        setFormData({
          version: editingRelease.version,
          title: editingRelease.title,
          notes: editingRelease.notes || '',
          platform_ids: editingRelease.platforms?.map(p => p.id) || [],
          architecture_ids: editingRelease.architectures?.map(a => a.id) || [],
          update_file: null,
          signature: editingRelease.signature || '',
          is_active: editingRelease.is_active,
          is_prerelease: editingRelease.is_prerelease,
          pub_date: editingRelease.pub_date ? new Date(editingRelease.pub_date).toISOString().slice(0, 16) : new Date().toISOString().slice(0, 16)
        });
      } else {
        setFormData({
          version: '',
          title: '',
          notes: '',
          platform_ids: [],
          architecture_ids: [],
          update_file: null,
          signature: '',
          is_active: true,
          is_prerelease: false,
          pub_date: new Date().toISOString().slice(0, 16)
        });
      }
    }
  }, [isOpen, editingRelease]);

  const loadPlatformsAndArchitectures = async () => {
    try {
      const [platformsData, architecturesData] = await Promise.all([
        apiClient.getPlatforms(),
        apiClient.getArchitectures()
      ]);
      setPlatforms(platformsData);
      setArchitectures(architecturesData);
    } catch (error) {
      console.error('Failed to load platforms and architectures:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (editingRelease) {
        await apiClient.updateRelease(editingRelease.id, formData);
      } else {
        await apiClient.createRelease(formData);
      }
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Failed to save release:', error);
      alert(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({ ...prev, update_file: file }));
  };

  const handlePlatformChange = (platformId: number, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      platform_ids: checked
        ? [...prev.platform_ids, platformId]
        : prev.platform_ids.filter(id => id !== platformId)
    }));
  };

  const handleArchitectureChange = (architectureId: number, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      architecture_ids: checked
        ? [...prev.architecture_ids, architectureId]
        : prev.architecture_ids.filter(id => id !== architectureId)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {editingRelease ? '编辑版本' : '发布新版本'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 版本号 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Tag className="inline h-4 w-4 mr-1" />
                版本号 *
              </label>
              <input
                type="text"
                required
                value={formData.version}
                onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="例如: 1.0.0"
              />
            </div>

            {/* 版本标题 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FileText className="inline h-4 w-4 mr-1" />
                版本标题 *
              </label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="例如: 重大功能更新"
              />
            </div>
          </div>

          {/* 版本说明 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              版本说明
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="支持 Markdown 格式..."
            />
          </div>

          {/* 平台选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              支持平台 *
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {platforms.map(platform => (
                <label key={platform.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.platform_ids.includes(platform.id)}
                    onChange={(e) => handlePlatformChange(platform.id, e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm">{platform.name}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 架构选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              支持架构 *
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {architectures.map(architecture => (
                <label key={architecture.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.architecture_ids.includes(architecture.id)}
                    onChange={(e) => handleArchitectureChange(architecture.id, e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm">{architecture.name}</span>
                </label>
              ))}
            </div>
          </div>

          {/* 文件上传 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Upload className="inline h-4 w-4 mr-1" />
              更新文件 {!editingRelease && '*'}
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              required={!editingRelease}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 文件签名 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文件签名
              </label>
              <input
                type="text"
                value={formData.signature}
                onChange={(e) => setFormData(prev => ({ ...prev, signature: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="SHA256 或其他签名"
              />
            </div>

            {/* 发布日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                发布日期 *
              </label>
              <input
                type="datetime-local"
                required
                value={formData.pub_date}
                onChange={(e) => setFormData(prev => ({ ...prev, pub_date: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 设置选项 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Settings className="inline h-4 w-4 mr-1" />
              设置选项
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-sm">启用此版本</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_prerelease}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_prerelease: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-sm">预发布版本</span>
              </label>
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? '保存中...' : (editingRelease ? '更新版本' : '发布版本')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReleaseForm;
