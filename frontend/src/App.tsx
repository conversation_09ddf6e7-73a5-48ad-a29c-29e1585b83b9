
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import Dashboard from './components/Dashboard';
import Platforms from './components/Platforms';
import Architectures from './components/Architectures';
import Releases from './components/Releases';
import UpdateChecks from './components/UpdateChecks';
import DownloadRecords from './components/DownloadRecords';
import Login from './components/Login';
import Register from './components/Register';
import ProtectedRoute from './components/ProtectedRoute';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* 公开路由 */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* 受保护的路由 */}
          <Route path="/*" element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/platforms" element={<Platforms />} />
                  <Route path="/architectures" element={<Architectures />} />
                  <Route path="/releases" element={<Releases />} />
                  <Route path="/update-checks" element={<UpdateChecks />} />
                  <Route path="/download-records" element={<DownloadRecords />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
