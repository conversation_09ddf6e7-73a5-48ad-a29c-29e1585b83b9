import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import type { User, AuthContextType, LoginRequest, RegisterRequest } from '../types';
import { apiClient } from '../api';
import { 
  setAuthToken, 
  getAuthToken, 
  setUserInfo, 
  getUserInfo, 
  clearAuthData, 
  isAuthenticated as checkIsAuthenticated 
} from '../utils/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        const savedToken = getAuthToken();
        const savedUser = getUserInfo();
        
        if (savedToken && savedUser && checkIsAuthenticated()) {
          setToken(savedToken);
          setUser(savedUser);
          
          // 验证token是否仍然有效，获取最新用户信息
          try {
            const currentUser = await apiClient.getCurrentUser();
            setUser(currentUser);
            setUserInfo(currentUser);
          } catch (error) {
            console.error('Token validation failed:', error);
            clearAuthData();
            setToken(null);
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        clearAuthData();
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      setIsLoading(true);
      const authResponse = await apiClient.login(credentials);
      
      // 存储token
      setAuthToken(authResponse.access_token);
      setToken(authResponse.access_token);
      
      // 获取用户信息
      const userInfo = await apiClient.getCurrentUser();
      setUser(userInfo);
      setUserInfo(userInfo);
      
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterRequest): Promise<void> => {
    try {
      setIsLoading(true);
      await apiClient.register(userData);
      
      // 注册成功后自动登录
      await login({
        username: userData.username,
        password: userData.password,
        remember_me: false
      });
      
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = (): void => {
    clearAuthData();
    setToken(null);
    setUser(null);
  };

  const isAuthenticated = Boolean(token && user && checkIsAuthenticated());

  const value: AuthContextType = {
    user,
    token,
    login,
    register,
    logout,
    isLoading,
    isAuthenticated,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
