import type {
  Platform,
  Architecture,
  Release,
  PlatformFormData,
  ArchitectureFormData,
  ReleaseFormData,
  BasicStats,
  User,
  LoginRequest,
  RegisterRequest,
  AuthToken,
  PaginatedResponse,
  UpdateCheckChartResponse
} from './types';
import { getAuthHeader } from './utils/auth';

const API_BASE_URL = '/p-box/api';

class ApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    // 如果是FormData，不设置Content-Type
    const isFormData = options.body instanceof FormData;
    const headers = isFormData
      ? { ...getAuthHeader(), ...options.headers }
      : { 'Content-Type': 'application/json', ...getAuthHeader(), ...options.headers };

    const response = await fetch(url, {
      headers,
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = `HTTP error! status: ${response.status}`;

      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.detail || errorMessage;
      } catch {
        // 如果不是JSON格式，使用默认错误消息
      }

      throw new Error(errorMessage);
    }

    return response.json();
  }

  // 平台相关 API
  async getPlatforms(): Promise<Platform[]> {
    const response = await this.request<{
      items: Platform[];
      total: number;
      totalPages: number;
      currentPage: number;
      pageSize: number;
    }>('/platforms/');
    return response.items;
  }

  async createPlatform(data: PlatformFormData): Promise<Platform> {
    return this.request<Platform>('/platforms/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 架构相关 API
  async getArchitectures(): Promise<Architecture[]> {
    const response = await this.request<{
      items: Architecture[];
      total: number;
      totalPages: number;
      currentPage: number;
      pageSize: number;
    }>('/architectures/');
    return response.items;
  }

  async createArchitecture(data: ArchitectureFormData): Promise<Architecture> {
    return this.request<Architecture>('/architectures/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 统计相关 API
  async getBasicStats(): Promise<BasicStats> {
    return this.request<BasicStats>('/stats/');
  }

  async getUpdateChecksChart(
    startDate?: string,
    endDate?: string,
    period: string = 'week'
  ): Promise<UpdateCheckChartResponse> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    params.append('period', period);

    return this.request<UpdateCheckChartResponse>(`/stats/admin/daily-unique-ips?${params.toString()}`);
  }

  // 认证相关 API
  async login(credentials: LoginRequest): Promise<AuthToken> {
    return this.request<AuthToken>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: RegisterRequest): Promise<User> {
    return this.request<User>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/auth/me');
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    return this.request<User>('/auth/me', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    return this.request<void>('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({
        old_password: oldPassword,
        new_password: newPassword,
      }),
    });
  }

  // 版本管理相关 API
  async getReleases(params?: {
    skip?: number;
    limit?: number;
    search?: string;
    platform_id?: number;
    architecture_id?: number;
  }): Promise<PaginatedResponse<Release>> {
    const searchParams = new URLSearchParams();
    if (params?.skip !== undefined) searchParams.append('skip', params.skip.toString());
    if (params?.limit !== undefined) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.platform_id) searchParams.append('platform_id', params.platform_id.toString());
    if (params?.architecture_id) searchParams.append('architecture_id', params.architecture_id.toString());

    const queryString = searchParams.toString();
    const endpoint = queryString ? `/releases/?${queryString}` : '/releases/';

    return this.request<PaginatedResponse<Release>>(endpoint);
  }

  async createRelease(data: ReleaseFormData): Promise<Release> {
    const formData = new FormData();
    formData.append('version', data.version);
    formData.append('title', data.title);
    formData.append('notes', data.notes || '');
    formData.append('platform_ids', data.platform_ids.join(','));
    formData.append('architecture_ids', data.architecture_ids.join(','));
    formData.append('signature', data.signature || '');
    formData.append('is_active', data.is_active.toString());
    formData.append('is_prerelease', data.is_prerelease.toString());
    formData.append('pub_date', data.pub_date);

    if (data.update_file) {
      formData.append('file', data.update_file);
    }

    return this.request<Release>('/releases/', {
      method: 'POST',
      body: formData,
    });
  }

  async updateRelease(id: number, data: Partial<ReleaseFormData>): Promise<Release> {
    const formData = new FormData();

    if (data.version !== undefined) formData.append('version', data.version);
    if (data.title !== undefined) formData.append('title', data.title);
    if (data.notes !== undefined) formData.append('notes', data.notes);
    if (data.platform_ids !== undefined) formData.append('platform_ids', data.platform_ids.join(','));
    if (data.architecture_ids !== undefined) formData.append('architecture_ids', data.architecture_ids.join(','));
    if (data.signature !== undefined) formData.append('signature', data.signature);
    if (data.is_active !== undefined) formData.append('is_active', data.is_active.toString());
    if (data.is_prerelease !== undefined) formData.append('is_prerelease', data.is_prerelease.toString());
    if (data.pub_date !== undefined) formData.append('pub_date', data.pub_date);

    if (data.update_file) {
      formData.append('file', data.update_file);
    }

    return this.request<Release>(`/releases/${id}`, {
      method: 'PUT',
      body: formData,
    });
  }

  async deleteRelease(id: number): Promise<void> {
    return this.request<void>(`/releases/${id}`, {
      method: 'DELETE',
    });
  }

  async getRelease(id: number): Promise<Release> {
    return this.request<Release>(`/releases/${id}`);
  }
}

export const apiClient = new ApiClient();
